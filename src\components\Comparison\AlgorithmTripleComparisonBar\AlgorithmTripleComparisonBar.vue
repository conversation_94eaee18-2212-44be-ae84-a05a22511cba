<!--
/**
 * @description 研判页面的三种算法展示的柱状图
 * <AUTHOR>
 * @version 1.0.0
 * @lastModified 2025/07/25
 *
  * @props {{
 *   data: { type: Array, required: true },       // 图表的原始数据（必须）
 *   clearSelectData: { type: Boolean, default: false },  // 清空数据标识（仅作标记用，与布尔值无关）
 *   threshold: { type: Number, default: 0.7 } // 算法阈值设置
 * }}
 * @emits {{
 *   checkboxChange: (payload: { id: string, checked: boolean }) => void,  // 复选框状态变化事件
 *   selectedData: (data: Array<object>) => void,                          // 选中数据集合变化事件
 *   overviewClick: (metrics: { accuracy: number, score: number }) => void  // 概览点击分析事件
 * }}
 */
-->
<template>
    <BaseBarChart ref="baseChart" :data="data" :clear-select-data="clearSelectData" :threshold="threshold"
        :options="chartOptions" @checkbox-change="handleCheckboxChange" @selected-data="emit('selectedData', $event)"
        @overview-click="emit('overviewClick', $event)" @chart-mounted="handleChartMounted"
        @data-changed="handleDataChanged" @chart-initialized="handleChartInitialized" />
</template>

<script setup>
import * as d3 from 'd3';
import { ref, computed, watch } from 'vue';
import { algorithmShortNames } from '@/api/algorithm';
import { toFixedDecimal } from '@/utils/tool';
import {
    calculateInterval,
    processChartData,
    calculateXPosition,
    calculateGroupStdDev,
    getAlgorithmFillColor,
    CHART_CONSTANTS
} from '@/utils/chartUtils';

import BaseBarChart from '@/components/Charts/BaseBarChart/index.vue';
import perfectMatch from '@/assets/svg/common/perfect-match.svg';
import textWeightedMatchingS from '@/assets/svg/common/text-weighted-matching.svg';
import textWeightedMatching from '@/assets/svg/common/text-weighted-matching-gray.svg';

const props = defineProps({
    // 图表的原始数据
    data: {
        type: Array
    },
    // 用来判断是不是要清空数据，只是一个标识，与true或false没有关系
    clearSelectData: {
        type: Boolean,
        default: false
    },
    // 设定阈值
    threshold: {
        type: Number,
        default: 0.7
    }
});

const emit = defineEmits(['checkboxChange', 'selectedData', 'overviewClick']);

const baseChart = ref(null);

// 图表配置选项（专门为AlgorithmTripleComparisonBar定制）
const chartOptions = computed(() => ({
    rectsPerGroup: CHART_CONSTANTS.RECTS_PER_GROUP_TRIPLE, // 3个一组
    varianceHeight: CHART_CONSTANTS.VARIANCE_HEIGHT, // 需要相似度差值区域
}));

// 内部状态变量
let processedData;
let interval;

/**
 * 处理复选框变化事件（修复事件格式问题）
 */
const handleCheckboxChange = (event) => {
    // 处理图例复选框点击
    if (typeof event === 'object' && event.id) {
        if (event.id === 'score' || event.id === 'textScore' || event.id === 'variance') {
            // 图例复选框，直接更新图表
            if (props.data && props.data.length > 0) {
                updateChart(props.data, false);
            }
            return;
        }
    }

    // 处理阈值复选框点击
    if (event === true || event === false) {
        // "显示全部"复选框
        emit('checkboxChange', event);
    } else if (Array.isArray(event)) {
        // 其他阈值复选框
        emit('checkboxChange', event);
    } else {
        // 兼容原有格式
        emit('checkboxChange', event);
    }
};

/**
 * 处理图表挂载完成事件
 */
const handleChartMounted = () => {
    // 基础图表挂载完成，可以进行特定的初始化
    if (props.data && props.data.length > 0) {
        // 更新图例复选框，添加相似度差值选项
        const { legendCheckbox } = baseChart.value;
        legendCheckbox.push({
            id: 'variance',
            label: '相似度差值',
            x: -130,
            y: -100,
            color: '#4DBDFE',
            isChecked: true
        });

        updateChart(props.data, true);
    }
};

/**
 * 处理数据变化事件
 */
const handleDataChanged = (newData) => {
    if (newData && baseChart.value) {
        const { currentData } = baseChart.value.barChart;
        if (currentData.value.size > 0) {
            // 保持选中状态
            const selector = `[data-id="${currentData.value.values().next().value?.algorithmCode}-${currentData.value.values().next().value?.portrait.portraitId}"]`;
            const clickedBar = d3.selectAll(selector);
            baseChart.value.barChart.updateElements(clickedBar, currentData.value.values().next().value);
        }

        updateChart(newData, false);
    }
};

/**
 * 处理图表重新初始化事件
 */
const handleChartInitialized = (newData) => {
    if (newData && newData.length > 0) {
        updateChart(newData, true);
    }
};

/**
 * 更新图表函数（AlgorithmTripleComparisonBar特有逻辑）
 */
const updateChart = (newData, isInit = false) => {
    try {
        if (!baseChart.value) return;

        const { g2, g4, altitudeHeight, svg } = baseChart.value.barChart.getSVGElements();
        const { state, currentData } = baseChart.value.barChart;

        // 清除之前的内容（保持部分重绘机制）
        g2.selectAll('*').remove();
        g4.selectAll('.operation-area').remove();
        svg.selectAll('.variance-area').remove(); // 清除相似度差值区域

        currentData.value.clear();

        const rectWidth = CHART_CONSTANTS.RECT_WIDTH;
        const rectsPerGroup = CHART_CONSTANTS.RECTS_PER_GROUP_TRIPLE; // 3个一组
        const startX = CHART_CONSTANTS.START_X;

        // 处理数据：每3个为一组，添加groupIndex
        processedData = processChartData(newData, rectsPerGroup);

        const totalRects = newData.length;
        const groups = Math.ceil(totalRects / rectsPerGroup);

        if (isInit) {
            // 初始化的时候算一次
            interval = calculateInterval(state.chartWidth - startX, rectWidth, totalRects, groups, rectsPerGroup);
        }

        // 获取图例复选框状态
        const { legendCheckbox } = baseChart.value;

        // 绘制相似度差值区域
        drawVarianceArea(processedData, groups, rectWidth, rectsPerGroup, startX, svg, state);

        // 绘制柱状图
        processedData.forEach((d, i) => {
            drawMainBars(d, i, rectWidth, altitudeHeight, g2, legendCheckbox, startX, rectsPerGroup);
            drawTextMatchingBars(d, i, rectWidth, altitudeHeight, g2, legendCheckbox, startX, rectsPerGroup);
            drawAlgorithmRect(d, i, rectWidth, g2, startX, rectsPerGroup);
            drawOperationArea(d, i, rectWidth, g4, currentData, startX, rectsPerGroup);
            drawPerfectMatchIcon(d, i, rectWidth, altitudeHeight, g2, startX, rectsPerGroup);
        });
    } catch (error) {
        console.error('更新图表错误', error);
    }
};

/**
 * 绘制相似度差值区域
 */
const drawVarianceArea = (processedData, groups, rectWidth, rectsPerGroup, startX, svg, state) => {
    const varianceData = calculateGroupStdDev(processedData);
    const varianceHeight = CHART_CONSTANTS.VARIANCE_HEIGHT;
    const { config } = baseChart.value.barChart;

    const varianceX = (_, i) => {
        return startX + i * (rectWidth * rectsPerGroup + interval);
    };

    varianceData.forEach((d, i) => {
        svg.append('rect')
            .attr('class', 'variance-area')
            .attr('x', varianceX(d, i) + config.margin.left)
            .attr('y', config.margin.top + varianceHeight - (d * varianceHeight))
            .attr('width', rectWidth * rectsPerGroup)
            .attr('height', Math.abs(d * varianceHeight))
            .attr('fill', 'transparent')
            .attr('stroke', '#768191');

        svg.append('text')
            .attr('class', 'variance-area')
            .attr('x', varianceX(d, i) + rectWidth + config.margin.left)
            .attr('y', config.margin.top + varianceHeight - (d * varianceHeight) - 4)
            .text(toFixedDecimal(d, 2))
            .style('font-size', '12px')
            .attr('fill', '#4DBFFF');
    });
};

/**
 * 绘制主要柱状图（人脸相似度）
 */
const drawMainBars = (d, i, rectWidth, altitudeHeight, g2, legendCheckbox, startX, rectsPerGroup) => {
    if (legendCheckbox[0].isChecked) {
        const x = calculateXPosition(d, startX, rectWidth, rectsPerGroup, interval);

        // 背景矩形
        g2.append('rect')
            .attr('class', 'main-bar')
            .attr('x', x)
            .attr('y', -altitudeHeight)
            .attr('width', rectWidth)
            .attr('height', Math.abs(altitudeHeight - d.score * altitudeHeight))
            .attr('fill', '#37404D');

        // 前景矩形（根据算法类型使用不同颜色）
        g2.append('rect')
            .attr('class', 'main-bar')
            .attr('x', x)
            .attr('y', -(d.score * altitudeHeight))
            .attr('width', rectWidth)
            .attr('height', d.score * altitudeHeight - 1)
            .attr('fill', getAlgorithmFillColor(d));

        // 分数文本
        g2.append('text')
            .attr('class', 'main-bar')
            .attr('x', x + rectWidth / 2)
            .attr('y', -(d.score * altitudeHeight) + 8)
            .attr('dy', '1em')
            .attr('fill', '#000')
            .text(() => toFixedDecimal(d.score, 2))
            .style('font-size', '12px')
            .style('text-anchor', 'middle');

        // 补偿分数文本
        g2.append('text')
            .attr('class', 'main-bar compensation-score')
            .attr('data-id', `${d.algorithmCode}-${d.portrait.portraitId}`)
            .attr('x', x + rectWidth / 2)
            .attr('y', -1 * altitudeHeight - 2)
            .attr('dy', '1em')
            .attr('fill', () => {
                if (d.scoreRank === 1 || d?.infoMatchFlag === 'EXACT_MATCH' || d.inGroupIndex === 0) {
                    return '#727982';
                } else {
                    return 'transparent';
                }
            })
            .text(() => {
                const score = parseFloat(toFixedDecimal(d.score, 2));
                const text = 1 - score;
                return toFixedDecimal(text, 2);
            })
            .style('font-size', '12px')
            .style('text-anchor', 'middle');
    }
};

/**
 * 绘制文本匹配度柱状图
 */
const drawTextMatchingBars = (d, i, rectWidth, altitudeHeight, g2, legendCheckbox, startX, rectsPerGroup) => {
    if (legendCheckbox[1].isChecked) {
        const x = calculateXPosition(d, startX, rectWidth, rectsPerGroup, interval);

        // 前景矩形（蓝色渐变）
        g2.append('rect')
            .attr('class', 'stacked-bar')
            .attr('x', x)
            .attr('y', 1)
            .attr('width', rectWidth)
            .attr('height', d?.infoMatch?.score * altitudeHeight)
            .attr('fill', 'url(#blueGradient)');

        // 背景矩形
        g2.append('rect')
            .attr('class', 'stacked-bar')
            .attr('x', x)
            .attr('y', d?.infoMatch?.score * altitudeHeight)
            .attr('width', rectWidth)
            .attr('height', Math.abs(altitudeHeight - d?.infoMatch?.score * altitudeHeight))
            .attr('fill', '#37404D');

        // 分数文本
        g2.append('text')
            .attr('class', 'stacked-bar')
            .attr('x', x + rectWidth / 2)
            .attr('y', d?.infoMatch?.score * altitudeHeight - 24)
            .attr('dy', '1em')
            .attr('fill', '#000')
            .text(() => toFixedDecimal(d?.infoMatch?.score, 2))
            .style('font-size', '12px')
            .style('text-anchor', 'middle');

        // 补偿分数文本
        g2.append('text')
            .attr('class', 'stacked-bar compensation-score')
            .attr('data-id', `${d.algorithmCode}-${d.portrait.portraitId}`)
            .attr('x', x + rectWidth / 2)
            .attr('y', 1 * altitudeHeight - 12)
            .attr('dy', '1em')
            .attr('fill', () => {
                if (d.scoreRank === 1 || d?.infoMatchFlag === 'EXACT_MATCH' || d.inGroupIndex === 0) {
                    return '#727982';
                }
                return 'transparent';
            })
            .text(() => {
                const score = parseFloat(toFixedDecimal(d?.infoMatch?.score, 2));
                const text = 1 - score;
                return toFixedDecimal(text, 2);
            })
            .style('font-size', '12px')
            .style('text-anchor', 'middle');

        // 文本匹配图标
        g2.append('image')
            .attr('class', 'stacked-bar text-matching-img')
            .attr('data-id', `${d.algorithmCode}-${d.portrait.portraitId}`)
            .attr('x', x + 8)
            .attr('y', 9)
            .attr('xlink:href', () => {
                if (d?.infoMatchFlag === 'EXACT_MATCH') {
                    return textWeightedMatchingS;
                } else if (d.scoreRank === 1 || d.inGroupIndex === 0) {
                    return textWeightedMatching;
                } else {
                    return null;
                }
            })
            .attr('width', 16)
            .attr('height', 16);
    }
};

/**
 * 绘制算法矩形和文本
 */
const drawAlgorithmRect = (d, i, rectWidth, g2, startX, rectsPerGroup) => {
    const x = calculateXPosition(d, startX, rectWidth, rectsPerGroup, interval);

    // 算法矩形
    g2.append('rect')
        .attr('data-id', `${d.algorithmCode}-${d.portrait.portraitId}`)
        .attr('class', 'main-bar algorithm-rect')
        .attr('x', x + 5)
        .attr('y', -25)
        .attr('width', rectWidth - 10)
        .attr('height', 17)
        .attr('fill', () => {
            if (d?.infoMatchFlag === 'EXACT_MATCH') {
                return 'url(#greenAndBlue)';
            } else {
                return 'none';
            }
        })
        .attr('stroke', () => {
            if (d?.infoMatchFlag === 'EXACT_MATCH') {
                return 'none';
            } else if (d.scoreRank === 1 || d.inGroupIndex === 0 || d.groupIndex === 0) {
                return 'white';
            } else {
                return 'none';
            }
        })
        .attr('stroke-width', 1)
        .attr('rx', 4)
        .attr('ry', 4);

    // 算法文本
    g2.append('text')
        .attr('data-id', `${d.algorithmCode}-${d.portrait.portraitId}`)
        .attr('class', 'main-bar algorithm-text')
        .attr('x', x + rectWidth / 2)
        .attr('y', -25)
        .attr('dy', '1em')
        .attr('fill', () => {
            if (d?.infoMatchFlag === 'EXACT_MATCH') {
                return 'black';
            } else if (d.scoreRank === 1 || d.inGroupIndex === 0 || d.groupIndex === 0) {
                return 'white';
            } else {
                return 'transparent';
            }
        })
        .text(() => algorithmShortNames[d?.algorithmCode])
        .style('font-size', '12px')
        .style('text-anchor', 'middle');

    // 人像标签（只在每组第一个显示）
    g2.append('text')
        .attr('class', 'text-label')
        .attr('data-id', `${d.algorithmCode}-${d.portrait.portraitId}`)
        .attr('x', x - 3)
        .attr('y', 100 + 14) // altitudeHeight + 14
        .attr('font-size', '12px')
        .attr('fill', () => {
            if (d?.infoMatchFlag === 'EXACT_MATCH') {
                return '#ffffff';
            }
            return '#68778A';
        })
        .text(() => {
            if (d.inGroupIndex === 0) {
                return `${d.xLabel} ${d?.portrait?.xm}`;
            } else {
                return '';
            }
        });
};

/**
 * 绘制操作区域
 */
const drawOperationArea = (d, i, rectWidth, g4, currentData, startX, rectsPerGroup) => {
    const x = calculateXPosition(d, startX, rectWidth, rectsPerGroup, interval);
    const { legendCheckbox } = baseChart.value;

    let operationY;
    let operationHeight;
    if (legendCheckbox[0].isChecked && legendCheckbox[1].isChecked) {
        operationY = -100; // -altitudeHeight
        operationHeight = 200; // altitudeHeight * 2
    } else if (legendCheckbox[0].isChecked && !legendCheckbox[1].isChecked) {
        operationY = -100; // -altitudeHeight
        operationHeight = 100; // altitudeHeight
    } else if (!legendCheckbox[0].isChecked && legendCheckbox[1].isChecked) {
        operationY = 0;
        operationHeight = 100; // altitudeHeight
    }

    // 顶部的操作图层rect
    g4.append('rect')
        .attr('data-id', `${d.algorithmCode}-${d.portrait.portraitId}`)
        .attr('class', 'operation-area')
        .attr('x', x)
        .attr('y', operationY)
        .attr('width', rectWidth)
        .attr('height', operationHeight)
        .attr('fill', 'transparent')
        .style('cursor', 'pointer')
        .on('click', () => {
            const flag = currentData.value.has(d);
            if (!flag && currentData.value.size > 0) {
                // 不是反选所以要清除其他的选择--为单选
                const selector = `[data-id="${currentData.value.values().next().value?.algorithmCode}-${currentData.value.values().next().value?.portrait.portraitId}"]`;
                const clickedBar = d3.selectAll(selector);
                baseChart.value.barChart.updateElements(clickedBar, currentData.value.values().next().value);
            }
            const selector = `[data-id="${d.algorithmCode}-${d.portrait.portraitId}"]`;
            const clickedBar = d3.selectAll(selector);
            baseChart.value.barChart.updateElements(clickedBar, d);
        });
};

/**
 * 绘制完全匹配图标
 */
const drawPerfectMatchIcon = (d, i, rectWidth, altitudeHeight, g2, startX, rectsPerGroup) => {
    if (d?.infoMatchFlag === 'EXACT_MATCH') {
        const x = calculateXPosition(d, startX, rectWidth, rectsPerGroup, interval);
        g2.append('image')
            .attr('x', x + 8)
            .attr('y', altitudeHeight + 12)
            .attr('xlink:href', perfectMatch)
            .attr('width', 16)
            .attr('height', 16);
    }
};

// 监听数据变化
watch(
    () => props.data,
    (newData) => {
        if (newData && baseChart.value && !baseChart.value.barChart.state.initData) {
            const { currentData } = baseChart.value.barChart;
            if (currentData.value.size > 0) {
                const selector = `[data-id="${currentData.value.values().next().value?.algorithmCode}-${currentData.value.values().next().value?.portrait.portraitId}"]`;
                const clickedBar = d3.selectAll(selector);
                baseChart.value.barChart.updateElements(clickedBar, currentData.value.values().next().value);
            }
            updateChart(newData);
        } else if (newData && baseChart.value && baseChart.value.barChart.state.initData) {
            const { svg } = baseChart.value.barChart.getSVGElements();
            if (svg) {
                svg.remove();
            }
            if (baseChart.value) {
                baseChart.value.initChart();
                updateChart(newData, true);
            }
        }
    },
    { deep: true }
);

// 监听阈值变化
watch(() => props.threshold, (newThreshold) => {
    if (newThreshold && baseChart.value && !baseChart.value.barChart.state.initData) {
        const { threshold } = baseChart.value;
        const { state } = baseChart.value.barChart;
        const { g3, g4 } = baseChart.value.barChart.getSVGElements();

        // 更新设定阈值线和对应的矩形
        const chartWidth = state.width - baseChart.value.barChart.config.margin.left - baseChart.value.barChart.config.margin.right;
        threshold.updateThresholdLineAndRect(newThreshold, chartWidth, g3, baseChart.value.barChart.getSVGElements().altitudeHeight);

        // 更新右侧的多选框
        g4.selectAll('.threshold-checkbox').remove();
        const updatedThresholdCheckbox = threshold.updateThresholdCheckbox(state.maxScore);
        baseChart.value.checkbox.addCheckbox({ value: updatedThresholdCheckbox }, 'threshold-checkbox', g4);
    }
});
</script>

<style scoped>
.bar-chart-container {
    width: 100%;
    height: 100%;
}
</style>