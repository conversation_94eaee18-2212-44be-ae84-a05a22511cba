<!--
 * @Author: gzr <EMAIL>
 * @Date: 2024-04-05 19:44:44
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-08-04 11:05:32
 * @FilePath: \platform-face-web\src\views\home\Index.vue
 * @Description: 炫酷暗色系首页
-->
<template>
  <div class="home-hero">
    <div class="particles-bg">
      <div v-for="n in 40" :key="n" class="particle" />
    </div>
    <div class="home-content">
      <LeftSideBar currentTopic="梦之队">
        <template #header>
          <div style="width: 100%;text-align: right;">

            新建专题
          </div>
        </template>
        <template #content>
          <div style="height: 100%;">1111111111111</div>

        </template>
      </LeftSideBar>
      <!-- <CommonUpload type="image">
        <a-button>上传</a-button>
      </CommonUpload> -->
      <!-- <DynamicNumber :start="0" :end="12345" :duration="2000" :decimals="2" />
      <RoseDiagram :circleList="dataList" :size="230" />
      <ProgressItems :items="items" layout="row" /> -->
      <div class="h-100 w-100">
        <!-- <div style="height: 500px;">
          <MatchingItemList :data="data"></MatchingItemList>
        </div>
        <div style=" height: 300px;">
          <CompareResultsBarGraph :data="data1"></CompareResultsBarGraph>
        </div> -->

        <!-- 三算法对比柱状图区域 -->
        <div style=" height: 320px; margin-top: 16px;">
          <AlgorithmTripleComparisonBar :data="tripleChartData" :clear-select-data="clearSelect" :threshold="threshold"
            @checkbox-change="onTripleCheckboxChange" @selected-data="onTripleSelectedData"
            @overview-click="onTripleOverviewClick" />
        </div>
      </div>

    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router';
import RoseDiagram from '@/components/Charts/RoseDiagram/index.vue'
import ProgressItems from '@/components/Charts/ProgressItems/index.vue'
import LeftSideBar from '@/components/Business/LeftSideBar/index.vue'
import MatchingItemList from '@/components/Comparison/MatchingItemList/index.vue'
import CompareResultsBarGraph from '../../components/Comparison/CompareResultsBarGraph/CompareResultsBarGraph.vue';
import AlgorithmTripleComparisonBar from '@/components/Comparison/AlgorithmTripleComparisonBar/AlgorithmTripleComparisonBar.vue'
import { ref } from 'vue'

const items = [
  { type: 'circle', value: 0.7, label: '上传', color: '#3a7adf', },
  { type: 'rect', value: 0.5, label: '智能分组', color: '#ff9800', radius: 12 },
  { type: 'triangle', value: 0.9, label: '比对', color: '#4caf50', }
];

const dataList = [
  { label: '归档', value: 30, color: '181,206,168,0.7' },
  { label: '上传', value: 60, color: '69,122,153,0.7' },
  { label: '归一', value: 80, color: '111,95,191,0.7' },
  { label: '比对', value: 70, color: '13,86,159,0.7' },
  { label: '关联', value: 50, color: '58,122,223,0.7' },
];

const router = useRouter();
const goExperience = () => {
  router.push('/topic');
};

// 三算法柱状图演示数据（8组，每组3个算法值）
const tripleChartData = ref([{
  "photoId": "photo_123",
  "algorithmCode": "ci_an",
  "score": 0.92,
  "scoreRank": 1,
  "infoMatch": {
    "score": 0.88,
    "items": []
  },
  "portrait": {
    "portraitId": "portrait_1",
    'xm': '张三'
  }
},
{
  "photoId": "photo_123",
  "algorithmCode": "ci",
  "score": 0.85,
  "scoreRank": 3,
  "infoMatch": {
    "score": 0.79,
    "items": []
  },
  "portrait": {
    "portraitId": "portrait_1",
    'xm': '张三'
  }
},
{
  "photoId": "photo_123",
  "algorithmCode": "an",
  "score": 0.78,
  "scoreRank": 5,
  "infoMatch": {
    "score": 0.72,
    "items": []
  },
  "portrait": {
    "portraitId": "portrait_1",
    'xm': '张三'
  }
},
{
  "photoId": "photo_123",
  "algorithmCode": "ci_an",
  "score": 0.89,
  "scoreRank": 2,
  "infoMatch": {
    "score": 0.83,
    "items": []
  },
  "portrait": {
    "portraitId": "portrait_2",
    'xm': '张三'
  }
},
{
  "photoId": "photo_123",
  "algorithmCode": "ci",
  "score": 0.76,
  "scoreRank": 4,
  "infoMatch": {
    "score": 0.68,
    "items": []
  },
  "portrait": {
    "portraitId": "portrait_2",
    'xm': '张三'
  }
},
{
  "photoId": "photo_123",
  "algorithmCode": "an",
  "score": 0.65,
  "scoreRank": 6,
  "infoMatch": {
    "score": 0.6,
    "items": []
  },
  "portrait": {
    "portraitId": "portrait_2",
    'xm': '张三'
  }
},])

// 控制清空选择与阈值
const clearSelect = ref(false)
const threshold = ref(0.72)

// 事件处理
const onTripleCheckboxChange = (payload) => {
  // payload: { id, checked }
  console.log('AlgorithmTripleComparisonBar checkboxChange', payload)
}

const onTripleSelectedData = (selected) => {
  // selected: Array<object>
  console.log('AlgorithmTripleComparisonBar selectedData', selected)
}

const onTripleOverviewClick = (metrics) => {
  // metrics: { accuracy, score }
  console.log('AlgorithmTripleComparisonBar overviewClick', metrics)
}

const data = [
  {
    "portrait": {
      "id": "202506211033598390000517873712",
      "portraitId": "202506211033598390000517873712",
      "libraryId": "68561615f197700cae8594f6",
      "libraryName": "中国乒乓球",
      "xm": "许昕",
      "xb": "MALE",
      "csrq": "1990-01-08T00:00:00.000+00:00",
      "zjh": "",
      "status": "ADDED",
      "standardPortraitImages": [
        {
          "imageId": "368e71c8-031b-412e-be90-2dd3d38ebe42",
          "idx": "0",
          "coordinate": [
            "171",
            "55",
            "84",
            "112"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "47ef94c9-504d-4cef-82d3-17113b53710c",
          "idx": "0",
          "coordinate": [
            "243",
            "131",
            "134",
            "181"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "12dfe4c7-b637-423d-8581-2c2e93f72e10",
          "idx": "0",
          "coordinate": [
            "69",
            "64",
            "159",
            "211"
          ],
          "isMultiFace": false
        }
      ],
      "createdAt": "2025-06-27T17:17:26.586Z",
      "updatedAt": "2025-06-27T17:17:26.586Z"
    },
    "score": 1,
    "scoreRank": 1,
    "infoMatching": {
      "score": 0,
      "items": []
    }
  },
  {
    "portrait": {
      "id": "202506121652479800000440518392",
      "portraitId": "202506121652479800000440518392",
      "libraryId": "6848e5ab0134f86ff3c09738",
      "libraryName": "9939测试库",
      "xm": "18780",
      "xb": "FEMALE",
      "csrq": "1997-03-10T00:00:00.000+00:00",
      "zjh": "15270119970310570X",
      "status": "ADDED",
      "standardPortraitImages": [
        {
          "imageId": "d1b9bc44-6058-41ca-8a7a-2fad3a6cc4be",
          "idx": "0",
          "coordinate": [
            "12",
            "11",
            "83",
            "101"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "9684a327-b7c6-451c-808a-0b31799ee496",
          "idx": "0",
          "coordinate": [
            "19",
            "8",
            "81",
            "104"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "a745ba34-a0de-4535-bc5c-c96d088b55ec",
          "idx": "0",
          "coordinate": [
            "11",
            "12",
            "84",
            "100"
          ],
          "isMultiFace": false
        }
      ],
      "createdAt": "2025-06-27T17:17:26.509Z",
      "updatedAt": "2025-06-27T17:17:26.509Z"
    },
    "score": 0.76293916,
    "scoreRank": 2,
    "infoMatching": {
      "score": 0,
      "items": []
    }
  },
  {
    "portrait": {
      "id": "202506211034022280000650631132",
      "portraitId": "202506211034022280000650631132",
      "libraryId": "68561615f197700cae8594f6",
      "libraryName": "中国乒乓球",
      "xm": "张继科",
      "xb": "MALE",
      "csrq": "1988-02-16T00:00:00.000+00:00",
      "zjh": "",
      "status": "ADDED",
      "standardPortraitImages": [
        {
          "imageId": "8d725dfe-6233-4a21-8371-30c744c34628",
          "idx": "0",
          "coordinate": [
            "245",
            "66",
            "139",
            "189"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "83d43457-7b69-49ff-864e-c42cb7f3aabb",
          "idx": "0",
          "coordinate": [
            "173",
            "81",
            "128",
            "169"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "f2be4d27-368c-4df4-896d-ffa8ed7344c0",
          "idx": "0",
          "coordinate": [
            "157",
            "71",
            "119",
            "163"
          ],
          "isMultiFace": false
        }
      ],
      "createdAt": "2025-06-27T17:17:26.588Z",
      "updatedAt": "2025-06-27T17:17:26.588Z"
    },
    "score": 0.7188764,
    "scoreRank": 3,
    "infoMatching": {
      "score": 0,
      "items": []
    }
  },
  {
    "portrait": {
      "id": "202506121654314200000546900512",
      "portraitId": "202506121654314200000546900512",
      "libraryId": "6848e5ab0134f86ff3c09738",
      "libraryName": "9939测试库",
      "xm": "10325",
      "xb": "FEMALE",
      "csrq": "1986-06-15T00:00:00.000+00:00",
      "zjh": "15270119860615636X",
      "status": "ADDED",
      "standardPortraitImages": [
        {
          "imageId": "b2b46fd0-0dc9-4d71-8c24-f5f2dcf29cf4",
          "idx": "0",
          "coordinate": [
            "23",
            "9",
            "78",
            "102"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "6f27ead3-daf4-4644-aeae-6ac010046ba8",
          "idx": "0",
          "coordinate": [
            "16",
            "13",
            "76",
            "99"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "c3dc6399-6dff-4631-bac4-01c1fa5a9e19",
          "idx": "0",
          "coordinate": [
            "18",
            "13",
            "73",
            "98"
          ],
          "isMultiFace": false
        }
      ],
      "createdAt": "2025-06-27T17:17:26.509Z",
      "updatedAt": "2025-06-27T17:17:26.509Z"
    },
    "score": 0.71082425,
    "scoreRank": 4,
    "infoMatching": {
      "score": 0,
      "items": []
    }
  },
  {
    "portrait": {
      "id": "202506121652276920000699472702",
      "portraitId": "202506121652276920000699472702",
      "libraryId": "6848e5ab0134f86ff3c09738",
      "libraryName": "9939测试库",
      "xm": "11625",
      "xb": "MALE",
      "csrq": "1996-07-17T00:00:00.000+00:00",
      "zjh": "15270119960717168X",
      "status": "ADDED",
      "standardPortraitImages": [
        {
          "imageId": "35df690f-7985-4a9e-8918-70b0ffde623a",
          "idx": "0",
          "coordinate": [
            "23",
            "7",
            "87",
            "105"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "d5d29976-450a-4b09-bd45-b0d9cf995374",
          "idx": "0",
          "coordinate": [
            "5",
            "8",
            "85",
            "104"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "9474377c-c973-4f9a-995d-3a5ab20705f0",
          "idx": "0",
          "coordinate": [
            "8",
            "13",
            "86",
            "99"
          ],
          "isMultiFace": false
        }
      ],
      "createdAt": "2025-06-27T17:17:26.509Z",
      "updatedAt": "2025-06-27T17:17:26.509Z"
    },
    "score": 0.7055664,
    "scoreRank": 5,
    "infoMatching": {
      "score": 0,
      "items": []
    }
  },
  {
    "portrait": {
      "id": "202506211033583100000852912342",
      "portraitId": "202506211033583100000852912342",
      "libraryId": "68561615f197700cae8594f6",
      "libraryName": "中国乒乓球",
      "xm": "樊振东",
      "xb": "MALE",
      "csrq": "1997-01-22T00:00:00.000+00:00",
      "zjh": "",
      "status": "ADDED",
      "standardPortraitImages": [
        {
          "imageId": "58e14540-80e9-4fdd-9edc-42854b34df15",
          "idx": "0",
          "coordinate": [
            "186",
            "46",
            "94",
            "117"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "b72fcdd9-b86a-4f07-8ac5-7e78a6857668",
          "idx": "0",
          "coordinate": [
            "140",
            "52",
            "148",
            "193"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "5364b757-dd6e-45eb-9ce9-2b153238177c",
          "idx": "0",
          "coordinate": [
            "248",
            "80",
            "148",
            "182"
          ],
          "isMultiFace": false
        }
      ],
      "createdAt": "2025-06-27T17:17:26.586Z",
      "updatedAt": "2025-06-27T17:17:26.586Z"
    },
    "score": 0.6962664,
    "scoreRank": 6,
    "infoMatching": {
      "score": 0,
      "items": []
    }
  },
  {
    "portrait": {
      "id": "202506121655367840000698116052",
      "portraitId": "202506121655367840000698116052",
      "libraryId": "6848e5ab0134f86ff3c09738",
      "libraryName": "9939测试库",
      "xm": "1382",
      "xb": "MALE",
      "csrq": "1984-08-16T00:00:00.000+00:00",
      "zjh": "15270119840816894X",
      "status": "ADDED",
      "standardPortraitImages": [
        {
          "imageId": "6c570653-5103-4d1e-9f09-be904fbfbab2",
          "idx": "0",
          "coordinate": [
            "24",
            "10",
            "80",
            "102"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "75c02b48-0e52-4b59-84c1-7813faf33083",
          "idx": "0",
          "coordinate": [
            "11",
            "6",
            "84",
            "106"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "b9c5b69c-fdee-4c99-a892-7ee89612cc35",
          "idx": "0",
          "coordinate": [
            "7",
            "9",
            "81",
            "103"
          ],
          "isMultiFace": false
        }
      ],
      "createdAt": "2025-06-27T17:17:26.509Z",
      "updatedAt": "2025-06-27T17:17:26.509Z"
    },
    "score": 0.6907064,
    "scoreRank": 7,
    "infoMatching": {
      "score": 0,
      "items": []
    }
  },
  {
    "portrait": {
      "id": "202506121652135400000117257242",
      "portraitId": "202506121652135400000117257242",
      "libraryId": "6848e5ab0134f86ff3c09738",
      "libraryName": "9939测试库",
      "xm": "17289",
      "xb": "MALE",
      "csrq": "1987-06-05T00:00:00.000+00:00",
      "zjh": "15270119870605652X",
      "status": "ADDED",
      "standardPortraitImages": [
        {
          "imageId": "65001045-f0f1-4346-8e5b-10db4531e432",
          "idx": "0",
          "coordinate": [
            "11",
            "10",
            "85",
            "102"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "bc07eebb-d173-4a90-97d1-948ddcad0206",
          "idx": "0",
          "coordinate": [
            "1",
            "7",
            "83",
            "105"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "fa55aece-8bac-4c68-9b75-9df2094b0272",
          "idx": "0",
          "coordinate": [
            "13",
            "5",
            "83",
            "107"
          ],
          "isMultiFace": false
        }
      ],
      "createdAt": "2025-06-27T17:17:26.509Z",
      "updatedAt": "2025-06-27T17:17:26.509Z"
    },
    "score": 0.6902609,
    "scoreRank": 8,
    "infoMatching": {
      "score": 0,
      "items": []
    }
  },
  {
    "portrait": {
      "id": "202506121648378770000315201852",
      "portraitId": "202506121648378770000315201852",
      "libraryId": "6848e5ab0134f86ff3c09738",
      "libraryName": "9939测试库",
      "xm": "14136",
      "xb": "FEMALE",
      "csrq": "1986-01-28T00:00:00.000+00:00",
      "zjh": "15270119860128108X",
      "status": "ADDED",
      "standardPortraitImages": [
        {
          "imageId": "2eb70063-cf94-43e0-8ad1-b272124fb72a",
          "idx": "0",
          "coordinate": [
            "7",
            "10",
            "76",
            "102"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "f72df71e-6665-41d2-9b68-09996b98a5ea",
          "idx": "0",
          "coordinate": [
            "26",
            "8",
            "84",
            "104"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "4dc94f17-cb4f-4c66-986b-dd1c8295122b",
          "idx": "0",
          "coordinate": [
            "10",
            "13",
            "84",
            "99"
          ],
          "isMultiFace": false
        }
      ],
      "createdAt": "2025-06-27T17:17:26.509Z",
      "updatedAt": "2025-06-27T17:17:26.509Z"
    },
    "score": 0.6890082,
    "scoreRank": 9,
    "infoMatching": {
      "score": 0,
      "items": []
    }
  },
  {
    "portrait": {
      "id": "202506121656370300000541959532",
      "portraitId": "202506121656370300000541959532",
      "libraryId": "6848e5ab0134f86ff3c09738",
      "libraryName": "9939测试库",
      "xm": "11363",
      "xb": "MALE",
      "csrq": "1981-05-16T00:00:00.000+00:00",
      "zjh": "15270119810516050X",
      "status": "ADDED",
      "standardPortraitImages": [
        {
          "imageId": "037a214b-74ae-485a-974c-f68dd8422c2a",
          "idx": "0",
          "coordinate": [
            "12",
            "10",
            "84",
            "102"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "11c897d4-599d-4521-8686-cd4104f28157",
          "idx": "0",
          "coordinate": [
            "13",
            "10",
            "85",
            "101"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "a07ab55d-bcd4-4a59-9590-8f8f2e7eb86a",
          "idx": "0",
          "coordinate": [
            "10",
            "12",
            "91",
            "100"
          ],
          "isMultiFace": false
        }
      ],
      "createdAt": "2025-06-27T17:17:26.509Z",
      "updatedAt": "2025-06-27T17:17:26.509Z"
    },
    "score": 0.68835664,
    "scoreRank": 10,
    "infoMatching": {
      "score": 0,
      "items": []
    }
  },
  {
    "portrait": {
      "id": "202506211034022250000257140662",
      "portraitId": "202506211034022250000257140662",
      "libraryId": "68561615f197700cae8594f6",
      "libraryName": "中国乒乓球",
      "xm": "周恺",
      "xb": "MALE",
      "csrq": "1996-03-01T00:00:00.000+00:00",
      "zjh": "",
      "status": "ADDED",
      "standardPortraitImages": [
        {
          "imageId": "5aee3208-06ae-47fe-9058-83e14fbff3eb",
          "idx": "0",
          "coordinate": [
            "278",
            "114",
            "133",
            "181"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "20934b6d-1f70-45ee-93f5-1ab9e9e19899",
          "idx": "0",
          "coordinate": [
            "250",
            "43",
            "66",
            "90"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "cd29a3b4-726e-4965-951a-bafa8a825135",
          "idx": "0",
          "coordinate": [
            "489",
            "133",
            "138",
            "177"
          ],
          "isMultiFace": false
        }
      ],
      "createdAt": "2025-06-27T17:17:26.588Z",
      "updatedAt": "2025-06-27T17:17:26.588Z"
    },
    "score": 0.6862629,
    "scoreRank": 11,
    "infoMatching": {
      "score": 0,
      "items": []
    }
  },
  {
    "portrait": {
      "id": "202506121655557950000441895562",
      "portraitId": "202506121655557950000441895562",
      "libraryId": "6848e5ab0134f86ff3c09738",
      "libraryName": "9939测试库",
      "xm": "10289",
      "xb": "MALE",
      "csrq": "1994-03-16T00:00:00.000+00:00",
      "zjh": "15270119940316411X",
      "status": "ADDED",
      "standardPortraitImages": [
        {
          "imageId": "a93596aa-5074-4a04-ab54-492bd9f5a6da",
          "idx": "0",
          "coordinate": [
            "1",
            "4",
            "78",
            "108"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "2d8cfd23-ba86-4c98-95a4-fe5eee78bf2f",
          "idx": "0",
          "coordinate": [
            "10",
            "16",
            "86",
            "96"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "6933fa55-ed82-42c3-996d-2a64c11606f0",
          "idx": "0",
          "coordinate": [
            "29",
            "15",
            "81",
            "96"
          ],
          "isMultiFace": false
        }
      ],
      "createdAt": "2025-06-27T17:17:26.509Z",
      "updatedAt": "2025-06-27T17:17:26.509Z"
    },
    "score": 0.6858668,
    "scoreRank": 12,
    "infoMatching": {
      "score": 0,
      "items": []
    }
  },
  {
    "portrait": {
      "id": "202506121650288010000998792322",
      "portraitId": "202506121650288010000998792322",
      "libraryId": "6848e5ab0134f86ff3c09738",
      "libraryName": "9939测试库",
      "xm": "11501",
      "xb": "MALE",
      "csrq": "1990-04-20T00:00:00.000+00:00",
      "zjh": "15270119900420494X",
      "status": "ADDED",
      "standardPortraitImages": [
        {
          "imageId": "ca16252c-4c25-4776-a474-974b6427bf5a",
          "idx": "0",
          "coordinate": [
            "23",
            "10",
            "80",
            "102"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "e8be9962-ece8-44e4-a589-87775e6b4858",
          "idx": "0",
          "coordinate": [
            "2",
            "2",
            "74",
            "109"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "91415099-166b-453e-8af0-b9415dc1c203",
          "idx": "0",
          "coordinate": [
            "13",
            "9",
            "87",
            "103"
          ],
          "isMultiFace": false
        }
      ],
      "createdAt": "2025-06-27T17:17:26.509Z",
      "updatedAt": "2025-06-27T17:17:26.509Z"
    },
    "score": 0.6851733,
    "scoreRank": 13,
    "infoMatching": {
      "score": 0,
      "items": []
    }
  },
  {
    "portrait": {
      "id": "202506121653258160000732982202",
      "portraitId": "202506121653258160000732982202",
      "libraryId": "6848e5ab0134f86ff3c09738",
      "libraryName": "9939测试库",
      "xm": "1457",
      "xb": "MALE",
      "csrq": "1992-04-24T00:00:00.000+00:00",
      "zjh": "15270119920424775X",
      "status": "ADDED",
      "standardPortraitImages": [
        {
          "imageId": "cd0f8e06-54bf-49f1-bbf4-99bd3820d6bb",
          "idx": "0",
          "coordinate": [
            "6",
            "11",
            "88",
            "101"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "f8054a0a-8574-42ee-bcb8-59d4850516e7",
          "idx": "0",
          "coordinate": [
            "13",
            "9",
            "86",
            "103"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "50dcc2f3-d7a7-44d0-8b47-96e362819864",
          "idx": "0",
          "coordinate": [
            "11",
            "10",
            "86",
            "102"
          ],
          "isMultiFace": false
        }
      ],
      "createdAt": "2025-06-27T17:17:26.509Z",
      "updatedAt": "2025-06-27T17:17:26.509Z"
    },
    "score": 0.6846812,
    "scoreRank": 14,
    "infoMatching": {
      "score": 0,
      "items": []
    }
  },
  {
    "portrait": {
      "id": "202506121649048880000966264362",
      "portraitId": "202506121649048880000966264362",
      "libraryId": "6848e5ab0134f86ff3c09738",
      "libraryName": "9939测试库",
      "xm": "10900",
      "xb": "FEMALE",
      "csrq": "1987-05-11T00:00:00.000+00:00",
      "zjh": "15270119870511931X",
      "status": "ADDED",
      "standardPortraitImages": [
        {
          "imageId": "d6bce686-826c-41ad-8c20-e2ba20b79102",
          "idx": "0",
          "coordinate": [
            "9",
            "6",
            "85",
            "106"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "99d17525-14c9-482c-8041-a856ec97f88e",
          "idx": "0",
          "coordinate": [
            "16",
            "9",
            "81",
            "103"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "2ba45189-a112-43b9-bb87-915a53b262a7",
          "idx": "0",
          "coordinate": [
            "25",
            "4",
            "79",
            "108"
          ],
          "isMultiFace": false
        }
      ],
      "createdAt": "2025-06-27T17:17:26.509Z",
      "updatedAt": "2025-06-27T17:17:26.509Z"
    },
    "score": 0.6827476,
    "scoreRank": 15,
    "infoMatching": {
      "score": 0,
      "items": []
    }
  }
]

const data1 = [
  {
    "portrait": {
      "id": "202506211033578740000493165082",
      "portraitId": "202506211033578740000493165082",
      "libraryId": "68561615f197700cae8594f6",
      "libraryName": "中国乒乓球",
      "xm": "孙颖莎",
      "xb": "FEMALE",
      "csrq": "2000-11-04T00:00:00.000+00:00",
      "zjh": "",
      "status": "ADDED",
      "standardPortraitImages": [
        {
          "imageId": "22ae205b-488b-4cc2-ab9c-b1f60af43be0",
          "idx": "0",
          "coordinate": [
            "386",
            "219",
            "127",
            "153"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "7c5acd81-8ccb-4a47-a41b-57fd67d6cbd3",
          "idx": "0",
          "coordinate": [
            "73",
            "110",
            "191",
            "243"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "00f18a65-cd46-46b3-9cc4-aff0eda9fb68",
          "idx": "0",
          "coordinate": [
            "223",
            "128",
            "121",
            "156"
          ],
          "isMultiFace": false
        }
      ],
      "createdAt": "2025-06-27T22:26:25.753Z",
      "updatedAt": "2025-06-27T22:26:25.753Z"
    },
    "score": 1,
    "scoreRank": 1,
    "infoMatching": {
      "score": 0.49,
      "items": [
        {
          "type": "NAME",
          "inputInfo": "孙颖莎",
          "portraitInfo": "孙颖莎",
          "matchProbability": 1,
          "weightedProbability": 0.3
        },
        {
          "type": "GENDER",
          "inputInfo": "female",
          "portraitInfo": "女",
          "matchProbability": 0,
          "weightedProbability": 0
        },
        {
          "type": "AGE",
          "inputInfo": 20,
          "portraitInfo": 25,
          "matchProbability": 0.95,
          "weightedProbability": 0.19
        }
      ]
    },
    "portraitId": "202506211033578740000493165082",
    "attachedAlgorithmCodes": [],
    "algorithmCode": "ci_an",
    "infoMatch": {
      "score": 0.92
    }
  },
  {
    "portrait": {
      "id": "202506211034002960000142837302",
      "portraitId": "202506211034002960000142837302",
      "libraryId": "68561615f197700cae8594f6",
      "libraryName": "中国乒乓球",
      "xm": "王曼昱",
      "xb": "FEMALE",
      "csrq": "1999-02-09T00:00:00.000+00:00",
      "zjh": "",
      "status": "ADDED",
      "standardPortraitImages": [
        {
          "imageId": "dda584d2-77a5-44a9-a50d-a52860f3cb49",
          "idx": "0",
          "coordinate": [
            "98",
            "197",
            "234",
            "289"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "9541fefc-a1c7-4464-956a-6294cf82477d",
          "idx": "0",
          "coordinate": [
            "129",
            "167",
            "223",
            "274"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "d5fd474a-f7ec-47b1-b5f5-452c10478d11",
          "idx": "0",
          "coordinate": [
            "433",
            "272",
            "354",
            "473"
          ],
          "isMultiFace": false
        }
      ],
      "createdAt": "2025-06-27T22:26:25.753Z",
      "updatedAt": "2025-06-27T22:26:25.753Z"
    },
    "score": 0.81027305,
    "scoreRank": 2,
    "infoMatching": {
      "score": 0.18800001,
      "items": [
        {
          "type": "NAME",
          "inputInfo": "孙颖莎",
          "portraitInfo": "王曼昱",
          "matchProbability": 0,
          "weightedProbability": 0
        },
        {
          "type": "GENDER",
          "inputInfo": "female",
          "portraitInfo": "女",
          "matchProbability": 0,
          "weightedProbability": 0
        },
        {
          "type": "AGE",
          "inputInfo": 20,
          "portraitInfo": 26,
          "matchProbability": 0.94,
          "weightedProbability": 0.18800001
        }
      ]
    },
    "portraitId": "202506211034002960000142837302",
    "attachedAlgorithmCodes": [],
    "algorithmCode": "ci_an",
    "infoMatch": {
      "score": 0.7875425573791008
    }
  },
  {
    "portrait": {
      "id": "202506121655274010000792915782",
      "portraitId": "202506121655274010000792915782",
      "libraryId": "6848e5ab0134f86ff3c09738",
      "libraryName": "9939测试库",
      "xm": "10684",
      "xb": "MALE",
      "csrq": "1980-07-15T00:00:00.000+00:00",
      "zjh": "15270119800715058X",
      "status": "ADDED",
      "standardPortraitImages": [
        {
          "imageId": "6c59dfb2-6e6b-42d1-9fb7-f1f3bd0ca524",
          "idx": "0",
          "coordinate": [
            "8",
            "7",
            "91",
            "105"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "7e657fa9-1ec5-4802-8ddd-43d03046c79d",
          "idx": "0",
          "coordinate": [
            "6",
            "22",
            "86",
            "90"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "a0227b18-a82a-4a72-8983-6938333f488d",
          "idx": "0",
          "coordinate": [
            "11",
            "7",
            "79",
            "105"
          ],
          "isMultiFace": false
        }
      ],
      "createdAt": "2025-06-27T22:26:25.815Z",
      "updatedAt": "2025-06-27T22:26:25.815Z"
    },
    "score": 0.7574101,
    "scoreRank": 3,
    "infoMatching": {
      "score": 0.15,
      "items": [
        {
          "type": "NAME",
          "inputInfo": "孙颖莎",
          "portraitInfo": "10684",
          "matchProbability": 0,
          "weightedProbability": 0
        },
        {
          "type": "GENDER",
          "inputInfo": "female",
          "portraitInfo": "男",
          "matchProbability": 0,
          "weightedProbability": 0
        },
        {
          "type": "AGE",
          "inputInfo": 20,
          "portraitInfo": 45,
          "matchProbability": 0.75,
          "weightedProbability": 0.15
        }
      ]
    },
    "portraitId": "202506121655274010000792915782",
    "attachedAlgorithmCodes": [],
    "algorithmCode": "ci_an",
    "infoMatch": {
      "score": 0.6118047129784547
    }
  },
  {
    "portrait": {
      "id": "202506121654589330000012732282",
      "portraitId": "202506121654589330000012732282",
      "libraryId": "6848e5ab0134f86ff3c09738",
      "libraryName": "9939测试库",
      "xm": "10723",
      "xb": "FEMALE",
      "csrq": "1994-07-10T00:00:00.000+00:00",
      "zjh": "15270119940710799X",
      "status": "ADDED",
      "standardPortraitImages": [
        {
          "imageId": "8fbfc0b8-699c-47a3-83fc-46d8c08fde40",
          "idx": "0",
          "coordinate": [
            "7",
            "5",
            "91",
            "107"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "6b9337d3-7059-4cd7-8e38-8468da4c8f37",
          "idx": "0",
          "coordinate": [
            "18",
            "9",
            "80",
            "101"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "1d1fa7fc-03fa-436c-b048-fd1b13cd5b38",
          "idx": "0",
          "coordinate": [
            "19",
            "19",
            "81",
            "93"
          ],
          "isMultiFace": false
        }
      ],
      "createdAt": "2025-06-27T22:26:25.815Z",
      "updatedAt": "2025-06-27T22:26:25.815Z"
    },
    "score": 0.7193674,
    "scoreRank": 4,
    "infoMatching": {
      "score": 0.178,
      "items": [
        {
          "type": "NAME",
          "inputInfo": "孙颖莎",
          "portraitInfo": "10723",
          "matchProbability": 0,
          "weightedProbability": 0
        },
        {
          "type": "GENDER",
          "inputInfo": "female",
          "portraitInfo": "女",
          "matchProbability": 0,
          "weightedProbability": 0
        },
        {
          "type": "AGE",
          "inputInfo": 20,
          "portraitInfo": 31,
          "matchProbability": 0.89,
          "weightedProbability": 0.178
        }
      ]
    },
    "portraitId": "202506121654589330000012732282",
    "attachedAlgorithmCodes": [],
    "algorithmCode": "ci_an",
    "infoMatch": {
      "score": 0.7305106483502184
    }
  },
  {
    "portrait": {
      "id": "202506121657489130000565611132",
      "portraitId": "202506121657489130000565611132",
      "libraryId": "6848e5ab0134f86ff3c09738",
      "libraryName": "9939测试库",
      "xm": "15124",
      "xb": "FEMALE",
      "csrq": "1988-06-26T00:00:00.000+00:00",
      "zjh": "15270119880626892X",
      "status": "ADDED",
      "standardPortraitImages": [
        {
          "imageId": "7217294f-2086-4287-8289-19fc68234c89",
          "idx": "0",
          "coordinate": [
            "14",
            "6",
            "77",
            "105"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "01b871c7-c97a-468f-80b0-253261c5439b",
          "idx": "0",
          "coordinate": [
            "14",
            "9",
            "76",
            "103"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "3d2e7222-2dc2-47aa-a616-3e7a7a5fdc0f",
          "idx": "0",
          "coordinate": [
            "22",
            "16",
            "79",
            "96"
          ],
          "isMultiFace": false
        }
      ],
      "createdAt": "2025-06-27T22:26:25.816Z",
      "updatedAt": "2025-06-27T22:26:25.816Z"
    },
    "score": 0.71721023,
    "scoreRank": 5,
    "infoMatching": {
      "score": 0.166,
      "items": [
        {
          "type": "NAME",
          "inputInfo": "孙颖莎",
          "portraitInfo": "15124",
          "matchProbability": 0,
          "weightedProbability": 0
        },
        {
          "type": "GENDER",
          "inputInfo": "female",
          "portraitInfo": "女",
          "matchProbability": 0,
          "weightedProbability": 0
        },
        {
          "type": "AGE",
          "inputInfo": 20,
          "portraitInfo": 37,
          "matchProbability": 0.83,
          "weightedProbability": 0.166
        }
      ]
    },
    "portraitId": "202506121657489130000565611132",
    "attachedAlgorithmCodes": [],
    "algorithmCode": "ci_an",
    "infoMatch": {
      "score": 0.6448254005465609
    }
  },
  {
    "portrait": {
      "id": "202506121654322230000170471062",
      "portraitId": "202506121654322230000170471062",
      "libraryId": "6848e5ab0134f86ff3c09738",
      "libraryName": "9939测试库",
      "xm": "11581",
      "xb": "MALE",
      "csrq": "1997-01-25T00:00:00.000+00:00",
      "zjh": "15270119970125982X",
      "status": "ADDED",
      "standardPortraitImages": [
        {
          "imageId": "47015ea6-938e-4203-b4c3-8e6533d939ba",
          "idx": "0",
          "coordinate": [
            "6",
            "19",
            "87",
            "93"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "468d4f90-4656-42a4-96f1-db31aba593db",
          "idx": "0",
          "coordinate": [
            "3",
            "21",
            "89",
            "91"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "d5fd6998-8937-4f5f-9fd6-ac092c43f0fd",
          "idx": "0",
          "coordinate": [
            "5",
            "22",
            "88",
            "90"
          ],
          "isMultiFace": false
        }
      ],
      "createdAt": "2025-06-27T22:26:25.815Z",
      "updatedAt": "2025-06-27T22:26:25.815Z"
    },
    "score": 0.7073572,
    "scoreRank": 6,
    "infoMatching": {
      "score": 0.184,
      "items": [
        {
          "type": "NAME",
          "inputInfo": "孙颖莎",
          "portraitInfo": "11581",
          "matchProbability": 0,
          "weightedProbability": 0
        },
        {
          "type": "GENDER",
          "inputInfo": "female",
          "portraitInfo": "男",
          "matchProbability": 0,
          "weightedProbability": 0
        },
        {
          "type": "AGE",
          "inputInfo": 20,
          "portraitInfo": 28,
          "matchProbability": 0.92,
          "weightedProbability": 0.184
        }
      ]
    },
    "portraitId": "202506121654322230000170471062",
    "attachedAlgorithmCodes": [],
    "algorithmCode": "ci_an",
    "infoMatch": {
      "score": 0.6065766474256172
    }
  },
  {
    "portrait": {
      "id": "202506121656483140000210318702",
      "portraitId": "202506121656483140000210318702",
      "libraryId": "6848e5ab0134f86ff3c09738",
      "libraryName": "9939测试库",
      "xm": "11281",
      "xb": "MALE",
      "csrq": "1983-07-28T00:00:00.000+00:00",
      "zjh": "15270119830728148X",
      "status": "ADDED",
      "standardPortraitImages": [
        {
          "imageId": "fd03adf8-b0f1-4c5c-b660-a134250978ed",
          "idx": "0",
          "coordinate": [
            "10",
            "12",
            "89",
            "100"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "7fe685b2-cb62-45ca-9b99-c901cbddc851",
          "idx": "0",
          "coordinate": [
            "9",
            "9",
            "85",
            "103"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "422f9f81-382a-454a-adff-deb34790e5c0",
          "idx": "0",
          "coordinate": [
            "5",
            "8",
            "88",
            "104"
          ],
          "isMultiFace": false
        }
      ],
      "createdAt": "2025-06-27T22:26:25.815Z",
      "updatedAt": "2025-06-27T22:26:25.815Z"
    },
    "score": 0.70203054,
    "scoreRank": 7,
    "infoMatching": {
      "score": 0.156,
      "items": [
        {
          "type": "NAME",
          "inputInfo": "孙颖莎",
          "portraitInfo": "11281",
          "matchProbability": 0,
          "weightedProbability": 0
        },
        {
          "type": "GENDER",
          "inputInfo": "female",
          "portraitInfo": "男",
          "matchProbability": 0,
          "weightedProbability": 0
        },
        {
          "type": "AGE",
          "inputInfo": 20,
          "portraitInfo": 42,
          "matchProbability": 0.78,
          "weightedProbability": 0.156
        }
      ]
    },
    "portraitId": "202506121656483140000210318702",
    "attachedAlgorithmCodes": [],
    "algorithmCode": "ci_an",
    "infoMatch": {
      "score": 0.6691399672493309
    }
  },
  {
    "portrait": {
      "id": "202506211034020810000195467041",
      "portraitId": "202506211034020810000195467041",
      "libraryId": "68561615f197700cae8594f6",
      "libraryName": "中国乒乓球",
      "xm": "李佳燚",
      "xb": "FEMALE",
      "csrq": "1994-01-08T00:00:00.000+00:00",
      "zjh": "",
      "status": "ADDED",
      "standardPortraitImages": [
        {
          "imageId": "20149463-3b30-47ac-87a7-a3005170279f",
          "idx": "0",
          "coordinate": [
            "429",
            "79",
            "96",
            "121"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "a3be13a4-6c0f-494d-824c-bb0570ef8132",
          "idx": "0",
          "coordinate": [
            "227",
            "113",
            "73",
            "93"
          ],
          "isMultiFace": false
        }
      ],
      "createdAt": "2025-06-27T22:26:25.753Z",
      "updatedAt": "2025-06-27T22:26:25.753Z"
    },
    "score": 0.7018717,
    "scoreRank": 8,
    "infoMatching": {
      "score": 0.178,
      "items": [
        {
          "type": "NAME",
          "inputInfo": "孙颖莎",
          "portraitInfo": "李佳燚",
          "matchProbability": 0,
          "weightedProbability": 0
        },
        {
          "type": "GENDER",
          "inputInfo": "female",
          "portraitInfo": "女",
          "matchProbability": 0,
          "weightedProbability": 0
        },
        {
          "type": "AGE",
          "inputInfo": 20,
          "portraitInfo": 31,
          "matchProbability": 0.89,
          "weightedProbability": 0.178
        }
      ]
    },
    "portraitId": "202506211034020810000195467041",
    "attachedAlgorithmCodes": [],
    "algorithmCode": "ci_an",
    "infoMatch": {
      "score": 0.7151490870342576
    }
  },
  {
    "portrait": {
      "id": "202506121651161880000596641402",
      "portraitId": "202506121651161880000596641402",
      "libraryId": "6848e5ab0134f86ff3c09738",
      "libraryName": "9939测试库",
      "xm": "18172",
      "xb": "MALE",
      "csrq": "1982-08-13T00:00:00.000+00:00",
      "zjh": "15270119820813592X",
      "status": "ADDED",
      "standardPortraitImages": [
        {
          "imageId": "e5d6c747-cf57-41d2-98d2-87b341e8043b",
          "idx": "0",
          "coordinate": [
            "13",
            "11",
            "83",
            "101"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "f26c15b1-ad16-42d2-a648-c27f1ef43a41",
          "idx": "0",
          "coordinate": [
            "12",
            "9",
            "84",
            "103"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "277492fe-c899-41e9-b193-10e25badd7ac",
          "idx": "0",
          "coordinate": [
            "13",
            "10",
            "87",
            "102"
          ],
          "isMultiFace": false
        }
      ],
      "createdAt": "2025-06-27T22:26:25.816Z",
      "updatedAt": "2025-06-27T22:26:25.816Z"
    },
    "score": 0.69105285,
    "scoreRank": 9,
    "infoMatching": {
      "score": 0.154,
      "items": [
        {
          "type": "NAME",
          "inputInfo": "孙颖莎",
          "portraitInfo": "18172",
          "matchProbability": 0,
          "weightedProbability": 0
        },
        {
          "type": "GENDER",
          "inputInfo": "female",
          "portraitInfo": "男",
          "matchProbability": 0,
          "weightedProbability": 0
        },
        {
          "type": "AGE",
          "inputInfo": 20,
          "portraitInfo": 43,
          "matchProbability": 0.77,
          "weightedProbability": 0.154
        }
      ]
    },
    "portraitId": "202506121651161880000596641402",
    "attachedAlgorithmCodes": [],
    "algorithmCode": "ci_an",
    "infoMatch": {
      "score": 0.7641853355991327
    }
  },
  {
    "portrait": {
      "id": "202506121647310610000580058782",
      "portraitId": "202506121647310610000580058782",
      "libraryId": "6848e5ab0134f86ff3c09738",
      "libraryName": "9939测试库",
      "xm": "10383",
      "xb": "FEMALE",
      "csrq": "1995-12-03T00:00:00.000+00:00",
      "zjh": "15270119951203425X",
      "status": "ADDED",
      "standardPortraitImages": [
        {
          "imageId": "de3ab33f-20a0-4b52-8e2a-133ef1110c56",
          "idx": "0",
          "coordinate": [
            "9",
            "6",
            "83",
            "106"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "9e3f22bc-7363-4b3b-a6c7-65f68c173216",
          "idx": "0",
          "coordinate": [
            "13",
            "25",
            "84",
            "87"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "459dff03-e7e8-489e-a6a4-2e77220fa6e5",
          "idx": "0",
          "coordinate": [
            "15",
            "11",
            "85",
            "101"
          ],
          "isMultiFace": false
        }
      ],
      "createdAt": "2025-06-27T22:26:25.815Z",
      "updatedAt": "2025-06-27T22:26:25.815Z"
    },
    "score": 0.6867639,
    "scoreRank": 10,
    "infoMatching": {
      "score": 0.17999999,
      "items": [
        {
          "type": "NAME",
          "inputInfo": "孙颖莎",
          "portraitInfo": "10383",
          "matchProbability": 0,
          "weightedProbability": 0
        },
        {
          "type": "GENDER",
          "inputInfo": "female",
          "portraitInfo": "女",
          "matchProbability": 0,
          "weightedProbability": 0
        },
        {
          "type": "AGE",
          "inputInfo": 20,
          "portraitInfo": 30,
          "matchProbability": 0.9,
          "weightedProbability": 0.17999999
        }
      ]
    },
    "portraitId": "202506121647310610000580058782",
    "attachedAlgorithmCodes": [],
    "algorithmCode": "ci_an",
    "infoMatch": {
      "score": 0.652839374815092
    }
  },
  {
    "portrait": {
      "id": "202506121649244410000715334362",
      "portraitId": "202506121649244410000715334362",
      "libraryId": "6848e5ab0134f86ff3c09738",
      "libraryName": "9939测试库",
      "xm": "10680",
      "xb": "MALE",
      "csrq": "1993-05-21T00:00:00.000+00:00",
      "zjh": "15270119930521146X",
      "status": "ADDED",
      "standardPortraitImages": [
        {
          "imageId": "051e120d-4332-460a-8983-ced36e27550e",
          "idx": "0",
          "coordinate": [
            "13",
            "12",
            "79",
            "98"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "b29eabb5-1d0b-4417-ad52-0842b40b5027",
          "idx": "0",
          "coordinate": [
            "15",
            "15",
            "84",
            "97"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "16877d8e-a50c-4418-8626-a6300de204f2",
          "idx": "0",
          "coordinate": [
            "9",
            "12",
            "84",
            "99"
          ],
          "isMultiFace": false
        }
      ],
      "createdAt": "2025-06-27T22:26:25.815Z",
      "updatedAt": "2025-06-27T22:26:25.815Z"
    },
    "score": 0.6856142,
    "scoreRank": 11,
    "infoMatching": {
      "score": 0.176,
      "items": [
        {
          "type": "NAME",
          "inputInfo": "孙颖莎",
          "portraitInfo": "10680",
          "matchProbability": 0,
          "weightedProbability": 0
        },
        {
          "type": "GENDER",
          "inputInfo": "female",
          "portraitInfo": "男",
          "matchProbability": 0,
          "weightedProbability": 0
        },
        {
          "type": "AGE",
          "inputInfo": 20,
          "portraitInfo": 32,
          "matchProbability": 0.88,
          "weightedProbability": 0.176
        }
      ]
    },
    "portraitId": "202506121649244410000715334362",
    "attachedAlgorithmCodes": [],
    "algorithmCode": "ci_an",
    "infoMatch": {
      "score": 0.7709058831708041
    }
  },
  {
    "portrait": {
      "id": "202506211034004430000161250842",
      "portraitId": "202506211034004430000161250842",
      "libraryId": "68561615f197700cae8594f6",
      "libraryName": "中国乒乓球",
      "xm": "马龙",
      "xb": "MALE",
      "csrq": "1988-10-20T00:00:00.000+00:00",
      "zjh": "",
      "status": "ADDED",
      "standardPortraitImages": [
        {
          "imageId": "99b8f16d-e20f-4bc5-a9c9-b933eeb883bb",
          "idx": "0",
          "coordinate": [
            "638",
            "461",
            "224",
            "285"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "78cd467b-604c-45c4-a6ef-fba6de8bb061",
          "idx": "0",
          "coordinate": [
            "377",
            "114",
            "73",
            "101"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "7c53b878-f821-4b10-8621-5e78aefc4442",
          "idx": "0",
          "coordinate": [
            "225",
            "93",
            "126",
            "166"
          ],
          "isMultiFace": false
        }
      ],
      "createdAt": "2025-06-27T22:26:25.753Z",
      "updatedAt": "2025-06-27T22:26:25.753Z"
    },
    "score": 0.68174374,
    "scoreRank": 12,
    "infoMatching": {
      "score": 0.166,
      "items": [
        {
          "type": "NAME",
          "inputInfo": "孙颖莎",
          "portraitInfo": "马龙",
          "matchProbability": 0,
          "weightedProbability": 0
        },
        {
          "type": "GENDER",
          "inputInfo": "female",
          "portraitInfo": "男",
          "matchProbability": 0,
          "weightedProbability": 0
        },
        {
          "type": "AGE",
          "inputInfo": 20,
          "portraitInfo": 37,
          "matchProbability": 0.83,
          "weightedProbability": 0.166
        }
      ]
    },
    "portraitId": "202506211034004430000161250842",
    "attachedAlgorithmCodes": [],
    "algorithmCode": "ci_an",
    "infoMatch": {
      "score": 0.7091775196484806
    }
  },
  {
    "portrait": {
      "id": "202506121657391380000958947752",
      "portraitId": "202506121657391380000958947752",
      "libraryId": "6848e5ab0134f86ff3c09738",
      "libraryName": "9939测试库",
      "xm": "11253",
      "xb": "MALE",
      "csrq": "1986-06-15T00:00:00.000+00:00",
      "zjh": "15270119860615291X",
      "status": "ADDED",
      "standardPortraitImages": [
        {
          "imageId": "faef2353-6173-4a69-9e1f-b141805ec6aa",
          "idx": "0",
          "coordinate": [
            "22",
            "8",
            "85",
            "103"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "954933e8-6c84-42cd-85d0-b6754f927c37",
          "idx": "0",
          "coordinate": [
            "18",
            "24",
            "90",
            "88"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "58715f8a-6bbf-4e8f-a75d-b3edfffea678",
          "idx": "0",
          "coordinate": [
            "9",
            "27",
            "87",
            "85"
          ],
          "isMultiFace": false
        }
      ],
      "createdAt": "2025-06-27T22:26:25.816Z",
      "updatedAt": "2025-06-27T22:26:25.816Z"
    },
    "score": 0.681087,
    "scoreRank": 13,
    "infoMatching": {
      "score": 0.162,
      "items": [
        {
          "type": "NAME",
          "inputInfo": "孙颖莎",
          "portraitInfo": "11253",
          "matchProbability": 0,
          "weightedProbability": 0
        },
        {
          "type": "GENDER",
          "inputInfo": "female",
          "portraitInfo": "男",
          "matchProbability": 0,
          "weightedProbability": 0
        },
        {
          "type": "AGE",
          "inputInfo": 20,
          "portraitInfo": 39,
          "matchProbability": 0.81,
          "weightedProbability": 0.162
        }
      ]
    },
    "portraitId": "202506121657391380000958947752",
    "attachedAlgorithmCodes": [],
    "algorithmCode": "ci_an",
    "infoMatch": {
      "score": 0.6373672364895993
    }
  },
  {
    "portrait": {
      "id": "202506121652466740000185662292",
      "portraitId": "202506121652466740000185662292",
      "libraryId": "6848e5ab0134f86ff3c09738",
      "libraryName": "9939测试库",
      "xm": "12661",
      "xb": "MALE",
      "csrq": "1995-08-02T00:00:00.000+00:00",
      "zjh": "15270119950802180X",
      "status": "ADDED",
      "standardPortraitImages": [
        {
          "imageId": "db9950e5-1837-4dd1-8a2c-49f47c4b08f8",
          "idx": "0",
          "coordinate": [
            "26",
            "4",
            "81",
            "108"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "b7ddd865-c7c8-4516-9f21-cc5e771e34cb",
          "idx": "0",
          "coordinate": [
            "4",
            "6",
            "88",
            "106"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "37c99048-0477-4733-bdcb-d00021920d5c",
          "idx": "0",
          "coordinate": [
            "22",
            "7",
            "78",
            "105"
          ],
          "isMultiFace": false
        }
      ],
      "createdAt": "2025-06-27T22:26:25.815Z",
      "updatedAt": "2025-06-27T22:26:25.815Z"
    },
    "score": 0.68067324,
    "scoreRank": 14,
    "infoMatching": {
      "score": 0.17999999,
      "items": [
        {
          "type": "NAME",
          "inputInfo": "孙颖莎",
          "portraitInfo": "12661",
          "matchProbability": 0,
          "weightedProbability": 0
        },
        {
          "type": "GENDER",
          "inputInfo": "female",
          "portraitInfo": "男",
          "matchProbability": 0,
          "weightedProbability": 0
        },
        {
          "type": "AGE",
          "inputInfo": 20,
          "portraitInfo": 30,
          "matchProbability": 0.9,
          "weightedProbability": 0.17999999
        }
      ]
    },
    "portraitId": "202506121652466740000185662292",
    "attachedAlgorithmCodes": [],
    "algorithmCode": "ci_an",
    "infoMatch": {
      "score": 0.7222759538242891
    }
  },
  {
    "portrait": {
      "id": "202506211034003600000694837022",
      "portraitId": "202506211034003600000694837022",
      "libraryId": "68561615f197700cae8594f6",
      "libraryName": "中国乒乓球",
      "xm": "梁靖崑",
      "xb": "MALE",
      "csrq": "1996-10-20T00:00:00.000+00:00",
      "zjh": "",
      "status": "ADDED",
      "standardPortraitImages": [
        {
          "imageId": "1c59ea08-3bf7-4d3c-b802-a08549082229",
          "idx": "0",
          "coordinate": [
            "614",
            "92",
            "144",
            "185"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "fa276c52-f1ef-4265-a4ac-b84e56ed9d1f",
          "idx": "0",
          "coordinate": [
            "124",
            "64",
            "128",
            "170"
          ],
          "isMultiFace": false
        },
        {
          "imageId": "bc7f2b81-d998-427b-89ba-bdbfcc30a92f",
          "idx": "0",
          "coordinate": [
            "187",
            "91",
            "101",
            "133"
          ],
          "isMultiFace": false
        }
      ],
      "createdAt": "2025-06-27T22:26:25.753Z",
      "updatedAt": "2025-06-27T22:26:25.753Z"
    },
    "score": 0.6800265,
    "scoreRank": 15,
    "infoMatching": {
      "score": 0.18200001,
      "items": [
        {
          "type": "NAME",
          "inputInfo": "孙颖莎",
          "portraitInfo": "梁靖崑",
          "matchProbability": 0,
          "weightedProbability": 0
        },
        {
          "type": "GENDER",
          "inputInfo": "female",
          "portraitInfo": "男",
          "matchProbability": 0,
          "weightedProbability": 0
        },
        {
          "type": "AGE",
          "inputInfo": 20,
          "portraitInfo": 29,
          "matchProbability": 0.91,
          "weightedProbability": 0.18200001
        }
      ]
    },
    "portraitId": "202506211034003600000694837022",
    "attachedAlgorithmCodes": [],
    "algorithmCode": "ci_an",
    "infoMatch": {
      "score": 0.6429054384803806
    }
  }
]
</script>


<style lang="scss" scoped>
.home-hero {
  position: relative;
  height: calc(100vh - 80px);
  width: 100%;
  background: linear-gradient(135deg, #181f2a 0%, #232946 100%);
  overflow: hidden;
}

.particles-bg {
  position: absolute;
  inset: 0;
  z-index: 1;
  pointer-events: none;
  overflow: hidden;
}

.particle {
  position: absolute;
  border-radius: 50%;
  background: rgba(102, 126, 234, 0.15);
  box-shadow: 0 0 16px 4px #667eea44, 0 0 32px 8px #764ba244;
  animation: float 8s linear infinite;
  opacity: 0.7;

  @for $i from 1 through 40 {
    &:nth-child(#{$i}) {
      width: random(16) + 8px;
      height: random(16) + 8px;
      left: percentage(random(100) / 100);
      top: percentage(random(100) / 100);
      animation-delay: random(8000) * -1ms;
      animation-duration: (random(8) + 6) * 1s;
    }
  }
}

@keyframes float {
  0% {
    transform: translateY(0) scale(1);
    opacity: 0.7;
  }

  50% {
    transform: translateY(-40px) scale(1.2);
    opacity: 1;
  }

  100% {
    transform: translateY(0) scale(1);
    opacity: 0.7;
  }
}

.home-content {
  position: relative;
  z-index: 2;
  color: #fff;
  width: 100vw;
  height: 100%;
  display: flex;
  gap: 20px;
}

.home-title {
  font-size: 3.2rem;
  font-weight: 800;
  letter-spacing: 2px;
  margin-bottom: 18px;
  background: linear-gradient(90deg, #667eea 10%, #764ba2 90%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 4px 32px #667eea44, 0 2px 8px #764ba244;
  animation: glow 2.5s ease-in-out infinite alternate;
}

@keyframes glow {
  from {
    text-shadow: 0 4px 32px #667eea44, 0 2px 8px #764ba244;
  }

  to {
    text-shadow: 0 0 64px #667eea99, 0 0 32px #764ba299;
  }
}

.home-subtitle {
  font-size: 1.3rem;
  color: #b6cfff;
  margin-bottom: 36px;
  letter-spacing: 1px;
  text-shadow: 0 2px 8px #23294699;
}

.home-btn {
  font-size: 1.2rem;
  padding: 0 2.5em;
  height: 48px;
  border-radius: 24px;
  background: linear-gradient(90deg, #667eea 10%, #764ba2 90%);
  border: none;
  box-shadow: 0 4px 24px #667eea33;
  transition: transform 0.2s, box-shadow 0.2s;
  font-weight: 600;
  letter-spacing: 1px;

  &:hover {
    transform: translateY(-4px) scale(1.04);
    box-shadow: 0 8px 32px #764ba244;
    background: linear-gradient(90deg, #764ba2 10%, #667eea 90%);
  }
}

@media (max-width: 600px) {
  .home-title {
    font-size: 2rem;
  }

  .home-subtitle {
    font-size: 1rem;
  }

  .home-btn {
    font-size: 1rem;
    height: 40px;
    padding: 0 1.5em;
  }
}
</style>
